import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    const formData = await request.json()
    const { firstName, lastName, email, phone, subject, message } = formData

    // Validate required fields
    if (!firstName || !lastName || !email || !subject || !message) {
      return NextResponse.json(
        { error: 'Tutti i campi obbligatori devono essere compilati' },
        { status: 400 }
      )
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Formato email non valido' },
        { status: 400 }
      )
    }

    const supabase = await createClient()

    // Store contact form submission in database
    const { error: insertError } = await supabase
      .from('contact_submissions')
      .insert({
        first_name: firstN<PERSON>,
        last_name: lastName,
        email,
        phone: phone || null,
        subject,
        message,
        created_at: new Date().toISOString()
      })

    if (insertError) {
      console.error('Error storing contact submission:', insertError)
      // Don't fail the request if we can't store it, just log it
    }

    // In a real application, you might want to:
    // 1. Send an email notification to the admin
    // 2. Send a confirmation email to the user
    // 3. Store the submission in a database
    
    // For now, we'll just return success
    return NextResponse.json({
      success: true,
      message: 'Messaggio inviato con successo! Ti risponderemo al più presto.'
    })

  } catch (error) {
    console.error('Error in contact form:', error)
    return NextResponse.json(
      { error: 'Errore interno del server. Riprova più tardi.' },
      { status: 500 }
    )
  }
}
