'use client'

import { useEffect, useState, useCallback, useMemo } from 'react'
import { createClient } from '@/lib/supabase/client'
import { useTranslations, useLocale } from 'next-intl'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { formatCurrency } from '@/lib/utils'
import { Truck, Save, ArrowLeft } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { AdminBackButton } from '@/components/admin/admin-back-button'
import { toast } from 'sonner'

interface ShippingRate {
  id: string
  country: string
  cost: number
  free_shipping_threshold: number
  estimated_days: string
  is_active: boolean
}

interface EditShippingPageProps {
  params: Promise<{ id: string; locale: string }>
}

export default function EditShippingPage({ params }: EditShippingPageProps) {
  const [resolvedParams, setResolvedParams] = useState<{ id: string; locale: string } | null>(null)
  const locale = useLocale()
  const router = useRouter()
  const t = useTranslations('admin')
  const supabase = useMemo(() => createClient(), [])

  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [authChecked, setAuthChecked] = useState(false)
  const [shippingRate, setShippingRate] = useState<ShippingRate | null>(null)
  const [formData, setFormData] = useState({
    country: '',
    cost: '',
    free_shipping_threshold: '',
    estimated_days: '',
    is_active: true
  })

  // Resolve params
  useEffect(() => {
    params.then(setResolvedParams)
  }, [params])

  const loadShippingRate = useCallback(async (id: string) => {
    try {
      console.log('Loading shipping rate:', id)

      const { data: shippingRateData, error } = await supabase
        .from('shipping_rates')
        .select('*')
        .eq('id', id)
        .single()

      if (error) {
        console.error('Error fetching shipping rate:', error)
        toast.error('Errore nel caricamento della tariffa di spedizione')
        router.push(`/${locale}/admin/shipping`)
        return
      }

      setShippingRate(shippingRateData)
      setFormData({
        country: shippingRateData.country,
        cost: shippingRateData.cost.toString(),
        free_shipping_threshold: shippingRateData.free_shipping_threshold.toString(),
        estimated_days: shippingRateData.estimated_days,
        is_active: shippingRateData.is_active
      })
      setLoading(false)
      console.log('Shipping rate loaded successfully')
    } catch (error) {
      console.error('Error loading shipping rate:', error)
      setLoading(false)
      toast.error('Errore nel caricamento della tariffa di spedizione')
      router.push(`/${locale}/admin/shipping`)
    }
  }, [supabase, locale, router])

  useEffect(() => {
    const checkAuthAndLoadData = async () => {
      try {
        console.log('Checking authentication...')
        const { data: { user } } = await supabase.auth.getUser()

        if (!user) {
          console.log('No user found, redirecting to login')
          setAuthChecked(true)
          router.push(`/${locale}/login`)
          return
        }

        console.log('User found:', user.email)

        const { data: profile, error: profileError } = await supabase
          .from('users')
          .select('is_admin')
          .eq('id', user.id)
          .single()

        if (profileError) {
          console.error('Error fetching user profile:', profileError)
          setAuthChecked(true)
          router.push(`/${locale}`)
          return
        }

        console.log('Profile loaded:', profile)

        if (!profile?.is_admin) {
          console.log('User is not admin, redirecting')
          setAuthChecked(true)
          router.push(`/${locale}`)
          return
        }

        console.log('User is admin, loading shipping rate data')
        setAuthChecked(true)
        if (resolvedParams?.id) {
          await loadShippingRate(resolvedParams.id)
        }
      } catch (error) {
        console.error('Error in checkAuthAndLoadData:', error)
        setAuthChecked(true)
        setLoading(false)
      }
    }

    if (!authChecked && resolvedParams) {
      checkAuthAndLoadData()
    }
  }, [authChecked, locale, router, supabase, loadShippingRate, resolvedParams])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!shippingRate) return

    // Validate form data
    if (!formData.country.trim()) {
      toast.error('Il paese è obbligatorio')
      return
    }

    const cost = parseFloat(formData.cost)
    const freeShippingThreshold = parseFloat(formData.free_shipping_threshold)

    if (isNaN(cost) || cost < 0) {
      toast.error('Il costo di spedizione deve essere un numero valido')
      return
    }

    if (isNaN(freeShippingThreshold) || freeShippingThreshold < 0) {
      toast.error('La soglia di spedizione gratuita deve essere un numero valido')
      return
    }

    if (!formData.estimated_days.trim()) {
      toast.error('Il tempo di consegna stimato è obbligatorio')
      return
    }

    setSaving(true)
    try {
      console.log('Updating shipping rate:', {
        id: shippingRate.id,
        country: formData.country,
        cost,
        free_shipping_threshold: freeShippingThreshold,
        estimated_days: formData.estimated_days,
        is_active: formData.is_active
      })

      const { error } = await supabase
        .from('shipping_rates')
        .update({
          country: formData.country.trim(),
          cost,
          free_shipping_threshold: freeShippingThreshold,
          estimated_days: formData.estimated_days.trim(),
          is_active: formData.is_active,
          updated_at: new Date().toISOString()
        })
        .eq('id', shippingRate.id)

      if (error) {
        console.error('Error updating shipping rate:', error)
        toast.error(`Errore nel salvataggio: ${error.message}`)
        return
      }

      console.log('Shipping rate updated successfully')
      toast.success('Tariffa di spedizione aggiornata con successo')
      router.push(`/${locale}/admin/shipping`)
    } catch (error) {
      console.error('Error updating shipping rate:', error)
      toast.error('Errore nel salvataggio della tariffa di spedizione')
    } finally {
      setSaving(false)
    }
  }

  if (!authChecked || loading) {
    return (
      <div className="container mx-auto py-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-muted-foreground">Caricamento...</p>
        </div>
      </div>
    )
  }

  if (!shippingRate) {
    return (
      <div className="container mx-auto py-8">
        <div className="text-center">
          <p className="text-muted-foreground">Tariffa di spedizione non trovata</p>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-8">
      {/* Back Button */}
      <div className="mb-6">
        <AdminBackButton />
      </div>

      <div className="max-w-2xl mx-auto">
        <div className="flex items-center gap-3 mb-8">
          <div className="w-10 h-10 rounded-full bg-primary flex items-center justify-center">
            <Truck className="h-5 w-5 text-primary-foreground" />
          </div>
          <div>
            <h1 className="text-3xl font-bold">Modifica Tariffa di Spedizione</h1>
            <p className="text-muted-foreground">
              Modifica le impostazioni di spedizione per {shippingRate.country}
            </p>
          </div>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Dettagli Spedizione</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="country">Paese</Label>
                  <Input
                    id="country"
                    value={formData.country}
                    onChange={(e) => setFormData({ ...formData, country: e.target.value })}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="cost">Costo Spedizione (CHF)</Label>
                  <Input
                    id="cost"
                    type="number"
                    step="0.01"
                    min="0"
                    value={formData.cost}
                    onChange={(e) => setFormData({ ...formData, cost: e.target.value })}
                    required
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="free_shipping_threshold">Soglia Spedizione Gratuita (CHF)</Label>
                  <Input
                    id="free_shipping_threshold"
                    type="number"
                    step="0.01"
                    min="0"
                    value={formData.free_shipping_threshold}
                    onChange={(e) => setFormData({ ...formData, free_shipping_threshold: e.target.value })}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="estimated_days">Tempo di Consegna Stimato</Label>
                  <Input
                    id="estimated_days"
                    value={formData.estimated_days}
                    onChange={(e) => setFormData({ ...formData, estimated_days: e.target.value })}
                    placeholder="es. 1-2 giorni lavorativi"
                    required
                  />
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="is_active"
                  checked={formData.is_active}
                  onCheckedChange={(checked) => setFormData({ ...formData, is_active: checked })}
                />
                <Label htmlFor="is_active">Attiva</Label>
              </div>

              <div className="flex gap-4 pt-6">
                <Button type="submit" disabled={saving}>
                  <Save className="mr-2 h-4 w-4" />
                  {saving ? 'Salvataggio...' : 'Salva Modifiche'}
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => router.push(`/${locale}/admin/shipping`)}
                >
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Annulla
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
