import { NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function GET() {
  try {
    const supabase = await createClient()
    
    // Get all available products to build filter options
    const { data: products, error } = await supabase
      .from('products')
      .select('category, coffee_type, brand, blend, machine_compatibility')
      .eq('is_available', true)

    if (error) {
      console.error('Error fetching products for filter options:', error)
      return NextResponse.json(
        { error: 'Failed to fetch filter options' },
        { status: 500 }
      )
    }

    // Build filter options from all products
    const categories = [...new Set(products.map(p => p.category).filter(Boolean))] as string[]
    
    const coffeeTypes = [...new Set(products.map(p => p.coffee_type).filter(Boolean))] as string[]
    
    const brands = [...new Set(products.map(p => p.brand).filter(Boolean))] as string[]
    
    const blends = [...new Set(products.map(p => p.blend).filter(Boolean))] as string[]
    
    const compatibilities = [...new Set(
      products
        .filter(p => p.machine_compatibility && p.machine_compatibility.length > 0)
        .flatMap(p => p.machine_compatibility || [])
        .filter(Boolean)
    )] as string[]

    return NextResponse.json({
      categories: categories.sort(),
      coffeeTypes: coffeeTypes.sort(),
      brands: brands.sort(),
      blends: blends.sort(),
      compatibilities: compatibilities.sort()
    })

  } catch (error) {
    console.error('Error in filter options API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
